import  { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, <PERSON>ygon, useMapE<PERSON>s, <PERSON><PERSON>, <PERSON>up, <PERSON> } from 'react-leaflet';
import L from 'leaflet';
import { Unit, Equipment, AITargetingResult, BattleSimulationResult } from '../types';
import { getInitialUnits, getInitialEquipment, getAITargetRecommendations, getInitialAnalyses } from '../data/mockData';
import MapControls from './MapControls';
import SearchBar from './SearchBar';
import BattleAnimation from './BattleAnimation';
import MapGallery from './MapGallery';
import { Target, X, ArrowDown, ZapIcon, Move, Layers, Mountain, Eye, Swords, FileText, Zap } from 'lucide-react';
import CircleRangeConfig from './CircleRangeConfig';
import TerrainAnalysisLayer from './TerrainAnalysisLayer';
import SpatialAnalysisControls from './SpatialAnalysisControls';
import { getUnitIcon, getEquipmentIcon } from './MilitaryIcons';
import { TerrainLayer, ObstacleLayer } from './TerrainAndObstacles';
import { renderCircleWithTerrain, renderCircleWithObstacles } from './AdvancedCircleAnalysis';
import RouteAnalysisLayer from './RouteAnalysisLayer';
import BattleSimulator from './BattleSimulator';
import BattleSimulationEffects from './BattleSimulationEffects';
import BattleForceSelector from './BattleForceSelector';
import ScenarioPlanner from './ScenarioPlanner';
import * as turf from '@turf/turf';
import RealBattleSimulation from './RealBattleSimulation';
import AdvancedBattleSimulation from './AdvancedBattleSimulation';
import MapBattleSimulation from './MapBattleSimulation';
import BattleResultFlash from './BattleResultFlash';

// Define unit icon component to display on map
const UnitIcon = ({ 
  type, 
  side, 
  isSelected = false,
  isTarget = false 
}: { 
  type: string; 
  side: 'friendly' | 'enemy';
  isSelected?: boolean;
  isTarget?: boolean;
}) => {
  // تحديد اللون الأساسي بناءً على الجانب
  let color = side === 'friendly' ? '#3b82f6' : '#ef4444';
  
  // تغيير اللون إذا كانت الوحدة مختارة أو مستهدفة
  if (isSelected) {
    color = '#10b981'; // أخضر للوحدات المختارة
  } else if (isTarget) {
    color = '#f59e0b'; // برتقالي للوحدات المستهدفة
  }
  
  // تحديد حجم الأيقونة (أكبر للوحدات المختارة)
  const size = isSelected || isTarget ? '30px' : '24px';
  
  // تحديد سمك الحدود (أكبر للوحدات المختارة)
  const borderWidth = isSelected || isTarget ? '3px' : '2px';
  
  const baseStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    backgroundColor: color,
    color: 'white',
    border: `${borderWidth} solid white`,
    boxShadow: isSelected || isTarget ? '0 0 8px rgba(255,255,255,0.8), 0 0 12px ' + color : '0 2px 4px rgba(0,0,0,0.3)',
    fontSize: '12px',
    fontWeight: 'bold',
    width: size,
    height: size,
    zIndex: isSelected || isTarget ? 1000 : 500
  };
  
  // Different sizes or styles could be applied based on unit type
  let style = { ...baseStyle };
  let text = '';
  
  switch (type) {
    case 'division':
      style = { ...style, width: '28px', height: '28px' };
      text = 'فق';
      break;
    case 'brigade':
      text = 'لو';
      break;
    case 'battalion':
      text = 'كت';
      break;
    case 'company':
      style = { ...style, width: '20px', height: '20px' };
      text = 'سر';
      break;
    default:
      text = 'وح';
  }
  
  return (
    <div style={style}>
      {text}
    </div>
  );
};

// Equipment icon component
const EquipmentIcon = ({ type }: { type: string }) => {
  const baseStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    backgroundColor: '#10b981',
    color: 'white',
    border: '2px solid white',
    boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
    width: '24px',
    height: '24px'
  };
  
  return (
    <div style={baseStyle}>
      {type.charAt(0).toUpperCase()}
    </div>
  );
};

// Custom map controller to handle events
function MapController({ onMapClick }: { onMapClick: (latlng: L.LatLng) => void }) {
  useMapEvents({
    click(e) {
      onMapClick(e.latlng);
    },
  });
  return null;
}

// Main MapView component
export default function MapView() {
  const [unitsData, setUnitsData] = useState<Unit[]>([]);
  const [equipmentData, setEquipmentData] = useState<Equipment[]>([]);
  const [selectedUnitId, setSelectedUnitId] = useState<string | null>(null);
  const [targetUnitId, setTargetUnitId] = useState<string | null>(null);
  const [targetingMode, setTargetingMode] = useState(false);

  // إضافة CSS للأيقونات المخصصة
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .custom-military-icon {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
      }
      .custom-military-icon div {
        pointer-events: none;
        user-select: none;
        font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji', sans-serif;
      }
      .custom-equipment-icon {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
      }
      .custom-equipment-icon div {
        pointer-events: none;
        user-select: none;
        font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji', sans-serif;
      }
      .leaflet-marker-icon {
        border-radius: 0 !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);
  const [moveMode, setMoveMode] = useState(false);
  const [aiRecommendation, setAiRecommendation] = useState<AITargetingResult | null>(null);
  const [showTargetingUI, setShowTargetingUI] = useState(false);
  const [showBattleScenarioUI, setShowBattleScenarioUI] = useState(false);
  const [animationConfig, setAnimationConfig] = useState<{
    source: [number, number];
    target: [number, number];
    isPlaying: boolean;
  } | null>(null);
  const [mapLayers, setMapLayers] = useState({
    friendly: true,
    enemy: true,
    equipment: true,
    terrain: false,
    obstacles: false,
  });
  const [zoomLevel, setZoomLevel] = useState(8);
  const [mapCenter, setMapCenter] = useState<[number, number]>([35, 38]); // Syria center
  const mapRef = useRef<L.Map | null>(null);
  const [satelliteView, setSatelliteView] = useState(false);
  const [mapImages] = useState([
    "https://images.unsplash.com/photo-1577086664693-894d8405334a?ixid=M3w3MjUzNDh8MHwxfHNlYXJjaHwxfHxzYXRlbGxpdGUlMjB2aWV3JTIwc3lyaWElMjBtYXAlMjBtaWxpdGFyeXxlbnwwfHx8fDE3NDg4NDk5MTl8MA&ixlib=rb-4.1.0&fit=fillmax&h=600&w=800",
    "https://images.unsplash.com/photo-1524661135-423995f22d0b?ixid=M3w3MjUzNDh8MHwxfHNlYXJjaHwyfHxzYXRlbGxpdGUlMjB2aWV3JTIwc3lyaWElMjBtYXAlMjBtaWxpdGFyeXxlbnwwfHx8fDE3NDg4NDk5MTl8MA&ixlib=rb-4.1.0&fit=fillmax&h=600&w=800"
  ]);
  const [showMapGallery, setShowMapGallery] = useState(false);

  // Advanced Battle Simulation states
  const [battleSelectionMode, setBattleSelectionMode] = useState(false);
  const [selectedFriendlyUnits, setSelectedFriendlyUnits] = useState<string[]>([]);
  const [selectedEnemyUnits, setSelectedEnemyUnits] = useState<string[]>([]);
  const [showAdvancedBattleSimulation, setShowAdvancedBattleSimulation] = useState(false);
  const [currentTargetingData, setCurrentTargetingData] = useState<{
    targetUnitId: string;
    attackerUnitId: string;
    weaponType: string;
    accuracy: number;
  } | null>(null);

  // Map Battle Simulation states
  const [mapBattleActive, setMapBattleActive] = useState(false);
  const [battleResult, setBattleResult] = useState<{
    winner: 'friendly' | 'enemy' | 'draw';
    friendlyLosses: number;
    enemyLosses: number;
    duration: number;
    summary: string;
  } | null>(null);
  const [moveSourceUnitId, setMoveSourceUnitId] = useState<string | null>(null);
  const [moveTargetPosition, setMoveTargetPosition] = useState<[number, number] | null>(null);
  const [battleScenario, setBattleScenario] = useState<{
    type: 'attack' | 'defend' | 'reinforce' | 'withdraw';
    attackerUnitIds: string[];
    defenderUnitIds: string[];
    targetPosition?: [number, number];
    probability?: number;
    estimatedLosses?: number;
    equipmentPositions?: { equipmentId: string; newPosition: [number, number] }[];
  } | null>(null);
  const [showSpatialAnalysisUI, setShowSpatialAnalysisUI] = useState(false);
  const [spatialAnalysisDistance, setSpatialAnalysisDistance] = useState(10);
  const [targetingDistance, setTargetingDistance] = useState(30);
  const [targetingReadiness, setTargetingReadiness] = useState(60);
  const [targetingWeaponType, setTargetingWeaponType] = useState<string>('all');
  const [showCircleAnalysisSingle, setShowCircleAnalysisSingle] = useState(false);
  const [showCircleAnalysisAll, setShowCircleAnalysisAll] = useState(false);
  const [showCircleAnalysisFlash, setShowCircleAnalysisFlash] = useState(false);
  const [showThreatenedUnitsFlash, setShowThreatenedUnitsFlash] = useState(false);
  const [showCircleAnalysisSingleWithTerrain, setShowCircleAnalysisSingleWithTerrain] = useState(false);
  const [showCircleAnalysisSingleWithObstacles, setShowCircleAnalysisSingleWithObstacles] = useState(false);
  const [showCircleAnalysisSingleWithBoth, setShowCircleAnalysisSingleWithBoth] = useState(false);
  // أنماط تحليل الدوائر الجديدة - 4 أنماط مختلفة
  const [circleAnalysisMode, setCircleAnalysisMode] = useState<'normal' | 'terrain' | 'obstacles' | 'both' | null>(null);
  const [showCircleAnalysisMultiple, setShowCircleAnalysisMultiple] = useState(false);
  const [focusedFriendlyUnit, setFocusedFriendlyUnit] = useState<string | null>(null);
  const [showEnemyThreatsToFriendly, setShowEnemyThreatsToFriendly] = useState(false);
  const [showEnemyThreatsFlash, setShowEnemyThreatsFlash] = useState(false);
  const [enemyThreatsToUnit, setEnemyThreatsToUnit] = useState<string[]>([]);
  const [multipleSelectedUnits, setMultipleSelectedUnits] = useState<string[]>([]);
  const [showCircleConfig, setShowCircleConfig] = useState(false);
  const [circleConfig, setCircleConfig] = useState(() => {
    const saved = localStorage.getItem('circle_range_config');
    return saved ? JSON.parse(saved) : null;
  });
  
  // Estados para análisis espacial avanzado
  const [showTerrainAnalysis, setShowTerrainAnalysis] = useState(false);
  const [showCoverageAnalysis, setShowCoverageAnalysis] = useState(false);
  const [showLOSAnalysis, setShowLOSAnalysis] = useState(false);
  const [showRouteAnalysis, setShowRouteAnalysis] = useState(false);
  const [routeStartUnitId, setRouteStartUnitId] = useState<string | null>(null);
  const [routeEndUnitId, setRouteEndUnitId] = useState<string | null>(null);
  
  // Estados para simulación de combate y planificación de escenarios
  const [showBattleSimulator, setShowBattleSimulator] = useState(false);
  const [showBattleForceSelector, setShowBattleForceSelector] = useState(false);
  const [battleSimulationResult, setBattleSimulationResult] = useState<BattleSimulationResult | null>(null);
  const [showBattleSimulationEffects, setShowBattleSimulationEffects] = useState(false);
  const [autoSimulationActive, setAutoSimulationActive] = useState(false);
  const [selectedBattleForces, setSelectedBattleForces] = useState<{
    friendlyUnits: string[];
    enemyUnits: string[];
    primaryWeapons: string[];
  } | null>(null);
  const [showScenarioPlanner, setShowScenarioPlanner] = useState(false);
  const [activeScenario, setActiveScenario] = useState<any | null>(null);
  const [showEditCirclesSingleWithObstacles, setShowEditCirclesSingleWithObstacles] = useState(false);
  const [showEditCirclesAllWithObstacles, setShowEditCirclesAllWithObstacles] = useState(false);
  const [showRealBattleSimulation, setShowRealBattleSimulation] = useState(false);
  
  // Load units and equipment on component mount
  useEffect(() => {
    // Clear localStorage to ensure we get fresh dispersed positions
    // Remove only unit and equipment position entries
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('unit_position_') || key.startsWith('equipment_position_')) {
        localStorage.removeItem(key);
      }
    });
    
    // Load initial data with dispersed positions
    const initialUnits = getInitialUnits();
    const initialEquipment = getInitialEquipment(initialUnits);
    setUnitsData(initialUnits);
    setEquipmentData(initialEquipment);
    
    // Save the new positions to localStorage for persistence
    initialUnits.forEach(unit => {
      localStorage.setItem(`unit_position_${unit.id}`, JSON.stringify(unit));
    });
    
    initialEquipment.forEach(eq => {
      localStorage.setItem(`equipment_position_${eq.id}`, JSON.stringify(eq));
    });
  }, []);

  // إضافة مرجع للخريطة في النافذة
  useEffect(() => {
    if (mapRef.current) {
      (window as any).map = mapRef.current;
    }
  }, [mapRef.current]);

  // البيانات الافتراضية للتضاريس - محاكاة ارتفاعات واقعية للمنطقة السورية
  const terrainData = [
    // جبال القلمون
    {
      id: 'mountain_1',
      name: 'جبال القلمون',
      type: 'mountain',
      elevation: 2800,
      coordinates: [[34.2, 36.6], [34.4, 36.8], [34.6, 36.7], [34.5, 36.5], [34.2, 36.6]],
      color: '#8B4513'
    },
    // التلال الساحلية
    {
      id: 'hills_1',
      name: 'التلال الساحلية',
      type: 'hills',
      elevation: 800,
      coordinates: [[35.8, 35.9], [36.1, 36.2], [36.2, 36.0], [35.9, 35.7], [35.8, 35.9]],
      color: '#CD853F'
    },
    // هضبة الجولان
    {
      id: 'plateau_1',
      name: 'هضبة الجولان',
      type: 'plateau',
      elevation: 1200,
      coordinates: [[33.1, 35.8], [33.3, 36.0], [33.4, 35.9], [33.2, 35.7], [33.1, 35.8]],
      color: '#A0522D'
    },
    // جبال الأنصارية
    {
      id: 'mountain_2',
      name: 'جبال الأنصارية',
      type: 'mountain',
      elevation: 1562,
      coordinates: [[35.2, 36.1], [35.5, 36.3], [35.6, 36.1], [35.3, 35.9], [35.2, 36.1]],
      color: '#8B4513'
    },
    // منطقة تضاريس كبيرة للاختبار - تغطي منطقة واسعة
    {
      id: 'test_terrain_1',
      name: 'منطقة جبلية للاختبار',
      type: 'mountain',
      elevation: 1800,
      coordinates: [[35.4, 36.0], [35.7, 36.0], [35.7, 36.4], [35.4, 36.4], [35.4, 36.0]],
      color: '#654321'
    },
    // منطقة تضاريس أخرى للاختبار
    {
      id: 'test_terrain_2',
      name: 'هضبة للاختبار',
      type: 'plateau',
      elevation: 1000,
      coordinates: [[35.3, 36.1], [35.6, 36.1], [35.6, 36.3], [35.3, 36.3], [35.3, 36.1]],
      color: '#8B7355'
    }
  ];

  // البيانات الافتراضية للعوائق - سواتر ترابية وحواجز دفاعية
  const obstaclesData = [
    {
      id: 'barrier_1',
      name: 'ساتر ترابي شمالي',
      type: 'earthwork',
      coordinates: [[34.8, 36.9], [34.9, 37.1], [34.95, 37.05], [34.85, 36.85]],
      height: 3,
      color: '#8B4513'
    },
    {
      id: 'barrier_2',
      name: 'خندق دفاعي',
      type: 'trench',
      coordinates: [[35.1, 36.2], [35.3, 36.4], [35.35, 36.35], [35.15, 36.15]],
      depth: 2,
      color: '#654321'
    },
    {
      id: 'barrier_3',
      name: 'حاجز خرساني',
      type: 'concrete_barrier',
      coordinates: [[34.5, 37.2], [34.7, 37.4], [34.75, 37.35], [34.55, 37.15]],
      height: 2.5,
      color: '#696969'
    },
    {
      id: 'barrier_4',
      name: 'أسلاك شائكة',
      type: 'wire_fence',
      coordinates: [[35.4, 36.8], [35.6, 37.0], [35.65, 36.95], [35.45, 36.75]],
      height: 1.5,
      color: '#2F4F4F'
    },
    {
      id: 'barrier_5',
      name: 'ساتر رملي جنوبي',
      type: 'sand_barrier',
      coordinates: [[33.8, 36.3], [34.0, 36.5], [34.05, 36.45], [33.85, 36.25]],
      height: 2.8,
      color: '#F4A460'
    },
    // عوائق كبيرة للاختبار
    {
      id: 'test_obstacle_1',
      name: 'منطقة عوائق للاختبار',
      type: 'concrete_barrier',
      coordinates: [[34.2, 36.2], [34.4, 36.2], [34.4, 36.4], [34.2, 36.4], [34.2, 36.2]],
      height: 4,
      color: '#696969'
    },
    {
      id: 'test_obstacle_2',
      name: 'حاجز كبير للاختبار',
      type: 'earthwork',
      coordinates: [[35.2, 37.2], [35.4, 37.2], [35.4, 37.4], [35.2, 37.4], [35.2, 37.2]],
      height: 5,
      color: '#8B4513'
    }
  ];
  
  // Get unit by ID helper
  const getUnitById = (id: string): Unit | undefined => {
    return unitsData.find((unit: Unit) => unit.id === id);
  };
  
  // Get stable position for equipment (avoid random repositioning)
  const getStableEquipmentPosition = (eq: Equipment, unit: Unit): [number, number] => {
    if (unit.location.type !== 'polygon') return [0, 0];
    
    const coords = unit.location.coordinates as number[][][];
    const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
    const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
    
    // Use equipment ID to create a stable offset
    const idSum = eq.id.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    const offsetScale = 0.01;
    const offsetX = ((idSum % 100) / 100) * offsetScale * 2 - offsetScale;
    const offsetY = ((idSum % 50) / 50) * offsetScale * 2 - offsetScale;
    
    return [centerLat + offsetX, centerLng + offsetY];
  };

  // دالة حساب مركز الوحدة
  const getUnitCenter = (unit: Unit): [number, number] => {
    if (unit.location.type === 'polygon') {
      const coords = unit.location.coordinates as number[][][];
      const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
      const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
      return [centerLat, centerLng];
    }
    return [0, 0];
  };

  // دالة للعثور على القوات الصديقة التي يمكنها الوصول إلى الهدف
  const getFriendlyUnitsInRange = (targetUnit: Unit) => {
    const [targetLat, targetLng] = getUnitCenter(targetUnit);

    return unitsData.filter(unit => {
      if (unit.side !== 'friendly') return false;

      const [unitLat, unitLng] = getUnitCenter(unit);
      const { blind, kill, nonkill } = getCircleRanges(unit);

      // حساب المسافة بين الوحدة والهدف
      const distance = getDistanceKm(unitLat, unitLng, targetLat, targetLng);

      // التحقق من وجود الهدف ضمن أي من الدوائر
      return distance <= Math.max(blind, kill, nonkill);
    });
  };

  // Handle unit click on map
  const handleUnitClick = (unitId: string) => {
    const clickedUnit = getUnitById(unitId);
    if (!clickedUnit) return;

    if (battleSelectionMode) {
      // وضع اختيار الوحدات للمعركة
      if (clickedUnit.side === 'friendly') {
        setSelectedFriendlyUnits(prev =>
          prev.includes(unitId)
            ? prev.filter(id => id !== unitId)
            : [...prev, unitId]
        );
      } else if (clickedUnit.side === 'enemy') {
        setSelectedEnemyUnits(prev =>
          prev.includes(unitId)
            ? prev.filter(id => id !== unitId)
            : [...prev, unitId]
        );
      }
      return;
    }

    // إذا تم النقر على وحدة صديقة، فعّل ميزة إظهار التهديدات
    if (clickedUnit.side === 'friendly') {
      if (focusedFriendlyUnit === unitId) {
        // إلغاء التركيز إذا تم النقر على نفس الوحدة
        setFocusedFriendlyUnit(null);
        setShowEnemyThreatsToFriendly(false);
      } else {
        // تركيز على الوحدة الجديدة
        setFocusedFriendlyUnit(unitId);
        setShowEnemyThreatsToFriendly(true);

        // التزويم على الوحدة
        const [lat, lng] = getUnitCenter(clickedUnit);
        if (mapRef.current) {
          mapRef.current.setView([lat, lng], 12);
        }
      }
      return;
    }

    if (targetingMode) {
      if (clickedUnit.side === 'enemy') {
        setTargetUnitId(unitId);

        // العثور على القوات الصديقة التي يمكنها الوصول إلى الهدف
        const friendlyUnitsInRange = getFriendlyUnitsInRange(clickedUnit);
        console.log('🎯 وضع الاستهداف الجديد - القوات القادرة على الوصول:', friendlyUnitsInRange.length);

        // إنشاء بيانات مخصصة للعرض
        const customRecommendation: AITargetingResult = {
          targetUnitId: unitId,
          bestAttackerUnitId: friendlyUnitsInRange.length > 0 ? friendlyUnitsInRange[0].id : '',
          recommendedWeapon: 'artillery',
          successProbability: 85,
          estimatedDamage: 70,
          collateralRisk: 15,
          friendlyUnitsInRange: friendlyUnitsInRange
        };

        setAiRecommendation(customRecommendation);
        setShowTargetingUI(true);

        // حفظ بيانات الاستهداف للمحاكاة المتقدمة
        if (friendlyUnitsInRange.length > 0) {
          setCurrentTargetingData({
            targetUnitId: unitId,
            attackerUnitId: friendlyUnitsInRange[0].id,
            weaponType: 'artillery',
            accuracy: 85
          });
        }
      } else {
        alert('يمكن استهداف وحدات العدو فقط');
      }
    } else if (moveMode) {
      if (moveSourceUnitId === null) {
        if (clickedUnit.side === 'friendly') {
          setMoveSourceUnitId(unitId);
          alert(`تم اختيار ${clickedUnit.name} للتحريك. الرجاء اختيار الموقع الهدف على الخريطة.`);
        } else {
          alert('يمكن تحريك القوات الصديقة فقط');
        }
      } else {
        // Cancel current selection if clicking another unit
        setMoveSourceUnitId(null);
        alert('تم إلغاء التحريك. الرجاء اختيار وحدة جديدة للتحريك.');
      }
    } else {
      if (showCircleAnalysisSingle || showCircleAnalysisSingleWithTerrain || showCircleAnalysisSingleWithObstacles) {
        setSelectedUnitId(unitId);
        setShowCircleAnalysisFlash(false);
      } else {
        setSelectedUnitId(unitId);

        // إظهار دوائر التحليل تلقائياً للوحدات الصديقة
        if (clickedUnit.side === 'friendly') {
          setShowCircleAnalysisSingle(true);
        }

        // Center map on selected unit
        if (clickedUnit.location.type === 'polygon') {
          const coords = clickedUnit.location.coordinates as number[][][];
          if (coords && coords[0] && coords[0][0]) {
            // Calculate center of polygon
            const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
            const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;

            setMapCenter([centerLat, centerLng]);
            if (mapRef.current) {
              mapRef.current.setView([centerLat, centerLng], 9);
            }
          }
        }
      }
    }
  };
  
  // Handle map click
  const handleMapClick = (latlng: L.LatLng) => {
    if (moveMode && moveSourceUnitId !== null) {
      // Use the clicked position as the target for unit movement
      setMoveTargetPosition([latlng.lat, latlng.lng]);
      
      // Get the source unit and its associated equipment
      const sourceUnit = getUnitById(moveSourceUnitId);
      if (sourceUnit && sourceUnit.location.type === 'polygon') {
        // Get all equipment associated with this unit
        const unitEquipment = equipmentData.filter(eq => eq.unitId === moveSourceUnitId);
        
        // Calculate new positions for equipment relative to the new unit position
        const equipmentPositions = unitEquipment.map(eq => {
          const [offsetLat, offsetLng] = getStableEquipmentPosition(eq, sourceUnit);
          const coords = sourceUnit.location.coordinates as number[][][];
          const latDiff = offsetLat - coords[0][0][0];
          const lngDiff = offsetLng - coords[0][0][1];
          
          return {
            equipmentId: eq.id,
            newPosition: [latlng.lat + latDiff, latlng.lng + lngDiff] as [number, number]
          };
        });
        
        // Show battle scenario UI with move details
        setBattleScenario({
          type: 'attack',
          attackerUnitIds: [moveSourceUnitId],
          defenderUnitIds: [],
          targetPosition: [latlng.lat, latlng.lng],
          probability: 85,
          estimatedLosses: 5,
          equipmentPositions: equipmentPositions
        });
        setShowBattleScenarioUI(true);
      }
    } else {
      // Deselect if clicking empty space
      setSelectedUnitId(null);
      // إخفاء دوائر التحليل عند إلغاء التحديد
      setShowCircleAnalysisSingle(false);
    }
  };
  
  // Toggle targeting mode
  const toggleTargetingMode = () => {
    setTargetingMode(!targetingMode);
    // Reset move mode if active
    if (moveMode) setMoveMode(false);
    
    if (targetingMode) {
      setTargetUnitId(null);
      setAiRecommendation(null);
      setShowTargetingUI(false);
      setShowCircleAnalysisMultiple(false);
      setMultipleSelectedUnits([]);
    }
  };
  
  // Toggle move mode
  const toggleMoveMode = () => {
    setMoveMode(!moveMode);
    // Reset targeting mode if active
    if (targetingMode) setTargetingMode(false);
    
    if (moveMode) {
      setMoveSourceUnitId(null);
      setMoveTargetPosition(null);
      setShowBattleScenarioUI(false);
    }
  };

  // Toggle battle selection mode
  const toggleBattleSelectionMode = () => {
    setBattleSelectionMode(!battleSelectionMode);

    // إعادة تعيين الأوضاع الأخرى
    if (targetingMode) setTargetingMode(false);
    if (moveMode) setMoveMode(false);

    if (battleSelectionMode) {
      // إعادة تعيين الوحدات المختارة
      setSelectedFriendlyUnits([]);
      setSelectedEnemyUnits([]);
    }
  };

  // Start advanced battle simulation
  const startAdvancedBattleSimulation = () => {
    if (selectedFriendlyUnits.length === 0 || selectedEnemyUnits.length === 0) {
      alert('يجب اختيار وحدات من كلا الطرفين لبدء المحاكاة');
      return;
    }

    // بدء المحاكاة على الخريطة
    setMapBattleActive(true);
    setBattleSelectionMode(false);
  };

  // Clear unit selections
  const clearUnitSelections = () => {
    setSelectedFriendlyUnits([]);
    setSelectedEnemyUnits([]);
  };

  // Handle battle completion
  const handleBattleComplete = (result: {
    winner: 'friendly' | 'enemy' | 'draw';
    friendlyLosses: number;
    enemyLosses: number;
    duration: number;
    summary: string;
  }) => {
    setBattleResult(result);
    setMapBattleActive(false);
    // مسح الوحدات المختارة بعد انتهاء المعركة
    setSelectedFriendlyUnits([]);
    setSelectedEnemyUnits([]);
  };

  // Execute attack based on AI recommendation
  const executeAttack = () => {
    if (!aiRecommendation || !targetUnitId) return;
    
    const targetUnit = getUnitById(targetUnitId);
    const attackerUnit = getUnitById(aiRecommendation.bestAttackerUnitId);
    
    if (!targetUnit || !attackerUnit) return;
    
    // Get coordinates for animation
    const targetCoords = (targetUnit.location.coordinates as number[][][])[0][0];
    const attackerCoords = (attackerUnit.location.coordinates as number[][][])[0][0];
    
    // Start animation
    setAnimationConfig({
      source: [attackerCoords[0], attackerCoords[1]],
      target: [targetCoords[0], targetCoords[1]],
      isPlaying: true
    });
    
    // Hide targeting UI during animation
    setShowTargetingUI(false);
  };
  
  // Execute battle scenario
  const executeBattleScenario = () => {
    if (!battleScenario) return;
    
    if (battleScenario.type === 'attack' && battleScenario.attackerUnitIds.length > 0) {
      const attackerUnit = getUnitById(battleScenario.attackerUnitIds[0]);
      
      if (attackerUnit && battleScenario.targetPosition && battleScenario.targetPosition.length === 2) {
        const targetPos = battleScenario.targetPosition;
        
        // Update unit position
        setUnitsData(prevUnits => prevUnits.map(unit => {
          if (unit.id === attackerUnit.id && unit.location.type === 'polygon') {
            const coords = unit.location.coordinates as number[][][];
            const oldCenterLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
            const oldCenterLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
            const latDiff = targetPos[0] - oldCenterLat;
            const lngDiff = targetPos[1] - oldCenterLng;
            const newCoords = coords[0].map(point => [point[0] + latDiff, point[1] + lngDiff]);
            
            // Store the new position in localStorage for persistence
            const updatedUnit = {
              ...unit,
              location: {
                ...unit.location,
                coordinates: [newCoords]
              }
            };
            localStorage.setItem(`unit_position_${unit.id}`, JSON.stringify(updatedUnit));
            
            return updatedUnit;
          }
          return unit;
        }));
        
        // Update equipment positions
        if (battleScenario.equipmentPositions) {
          setEquipmentData(prevEq => prevEq.map(eq => {
            const eqPos = battleScenario.equipmentPositions!.find(e => e.equipmentId === eq.id);
            if (eqPos) {
              const updatedEq = {
                ...eq,
                unitId: attackerUnit.id
              };
              // Store equipment position in localStorage
              localStorage.setItem(`equipment_position_${eq.id}`, JSON.stringify(updatedEq));
              return updatedEq;
            }
            return eq;
          }));
        }
        
        setShowBattleScenarioUI(false);
        setMoveMode(false);
        setMoveSourceUnitId(null);
        setMoveTargetPosition(null);
        setBattleScenario(null);
        alert('تم نقل الوحدة والعتاد بنجاح!');
      }
    }
  };
  
  // Handle animation completion
  const handleAnimationComplete = () => {
    setAnimationConfig(null);
    setTargetingMode(false);
    setTargetUnitId(null);
    setAiRecommendation(null);
    setMoveMode(false);
    setMoveSourceUnitId(null);
    setMoveTargetPosition(null);
    setBattleScenario(null);
    
    // Simulate damage to enemy unit (this would be updated in a real system)
    alert('تم تنفيذ العملية بنجاح!');
  };
  
  // Manejadores para análisis espacial
  const handleToggleTerrainAnalysis = (show: boolean) => {
    setShowTerrainAnalysis(show);
  };
  
  const handleToggleCoverageAnalysis = (show: boolean) => {
    setShowCoverageAnalysis(show);
  };
  
  const handleToggleLOSAnalysis = (show: boolean) => {
    setShowLOSAnalysis(show);
  };
  
  const handleToggleRouteAnalysis = (show: boolean) => {
    setShowRouteAnalysis(show);
    if (!show) {
      setRouteStartUnitId(null);
      setRouteEndUnitId(null);
    }
  };
  
  const handleCalculateOptimalRoute = (startId: string, endId: string) => {
    setRouteStartUnitId(startId);
    setRouteEndUnitId(endId);
  };
  
  // Manejadores para simulación de combate
  const handleOpenBattleSimulator = () => {
    // عند النقر على زر محاكاة المعركة، نفتح واجهة اختيار القوات
    setShowBattleForceSelector(true);
  };
  
  const handleCloseBattleForceSelector = () => {
    setShowBattleForceSelector(false);
  };
  
  const handleBattleForceSelection = (selectedData: {
    friendlyUnits: string[];
    enemyUnits: string[];
    primaryWeapons: string[];
  }) => {
    // حفظ القوات المختارة
    setSelectedBattleForces(selectedData);
    
    // إغلاق واجهة الاختيار
    setShowBattleForceSelector(false);
    
    // بدء المحاكاة التلقائية
    startBattleSimulation(selectedData);
  };
  
  const startBattleSimulation = (selectedForces: {
    friendlyUnits: string[];
    enemyUnits: string[];
    primaryWeapons: string[];
  }) => {
    // تفعيل المحاكاة
    setAutoSimulationActive(true);
    
    // الحصول على الوحدات المختارة
    const selectedFriendlyUnits = selectedForces.friendlyUnits;
    const selectedEnemyUnits = selectedForces.enemyUnits;
    const selectedWeapons = selectedForces.primaryWeapons;
    
    // حساب قوة الهجوم بناءً على الوحدات والأسلحة المختارة
    const friendlyStrength = calculateForceStrength(selectedFriendlyUnits, selectedWeapons);
    const enemyStrength = calculateDefenseStrength(selectedEnemyUnits);
    
    // حساب احتمالية النجاح
    const totalStrength = friendlyStrength + enemyStrength;
    const winningProbability = Math.min(95, Math.max(5, Math.round((friendlyStrength / totalStrength) * 100)));
    
    // حساب الخسائر المتوقعة
    const personnelLosses = Math.round(50 + (100 - winningProbability) * 3);
    
    // حساب خسائر المعدات بناءً على الأسلحة المختارة
    const equipmentLosses: Record<string, number> = {
      tank: 0, apc: 0, artillery: 0, anti_air: 0, helicopter: 0,
      fighter: 0, drone: 0, missile: 0, radar: 0, logistics: 0,
      medical: 0, command: 0
    };
    
    // زيادة خسائر المعدات المستخدمة في المعركة
    selectedWeapons.forEach(weaponId => {
      const weapon = equipmentData.find(e => e.id === weaponId);
      if (weapon) {
        equipmentLosses[weapon.type] += 1 + Math.floor(Math.random() * 3);
      }
    });
    
    // إنشاء العوامل الرئيسية بناءً على القوات المختارة
    const keyFactors = [];
    
    if (friendlyStrength > enemyStrength * 1.5) {
      keyFactors.push('تفوق قوات الهجوم من حيث العدد والعتاد');
    } else if (enemyStrength > friendlyStrength * 1.5) {
      keyFactors.push('تفوق قوات الدفاع من حيث العدد والعتاد');
    }
    
    if (selectedWeapons.some(id => {
      const weapon = equipmentData.find(e => e.id === id);
      return weapon && (weapon.type === 'fighter' || weapon.type === 'helicopter');
    })) {
      keyFactors.push('استخدام القوة الجوية يعزز فرص النجاح');
    }
    
    if (selectedWeapons.some(id => {
      const weapon = equipmentData.find(e => e.id === id);
      return weapon && weapon.type === 'artillery';
    })) {
      keyFactors.push('الدعم المدفعي يمهد للهجوم البري');
    }
    
    // إنشاء نتيجة المحاكاة
    const simulationResult: BattleSimulationResult = {
      winningProbability,
      estimatedLosses: {
        personnel: personnelLosses,
        equipment: equipmentLosses as Record<any, number>
      },
      duration: 4 + Math.floor(Math.random() * 12),
      keyFactors
    };
    
    // تعيين نتيجة المحاكاة وتفعيل التأثيرات المرئية
    setBattleSimulationResult(simulationResult);
    setShowBattleSimulationEffects(true);
  };
  
  // حساب قوة الهجوم بناءً على الوحدات والأسلحة المختارة
  const calculateForceStrength = (unitIds: string[], weaponIds: string[]): number => {
    let strength = 0;
    
    // قوة الوحدات
    unitIds.forEach(id => {
      const unit = unitsData.find(u => u.id === id);
      if (unit) {
        strength += unit.personnelCount * (unit.readiness / 100);
      }
    });
    
    // قوة الأسلحة
    weaponIds.forEach(id => {
      const weapon = equipmentData.find(e => e.id === id);
      if (weapon) {
        const weaponMultiplier = 
          weapon.type === 'tank' ? 10 :
          weapon.type === 'artillery' ? 8 :
          weapon.type === 'fighter' || weapon.type === 'helicopter' ? 15 :
          weapon.type === 'missile' ? 12 : 5;
        
        strength += (weapon.firepower || 5) * weaponMultiplier;
      }
    });
    
    return strength;
  };
  
  // حساب قوة الدفاع
  const calculateDefenseStrength = (unitIds: string[]): number => {
    let strength = 0;
    
    unitIds.forEach(id => {
      const unit = unitsData.find(u => u.id === id);
      if (unit) {
        strength += unit.personnelCount * (unit.readiness / 100) * 1.2; // المدافع له ميزة
        
        // إضافة قوة معدات الوحدة
        unit.equipmentIds.forEach(eqId => {
          const eq = equipmentData.find(e => e.id === eqId);
          if (eq && eq.status === 'operational') {
            strength += (eq.firepower || 3) * 5;
          }
        });
      }
    });
    
    return strength;
  };
  
  const handleCloseBattleSimulator = () => {
    setShowBattleSimulator(false);
  };
  
  const handleSimulationComplete = (result: BattleSimulationResult) => {
    setBattleSimulationResult(result);
  };
  
  const handleBattleEffectsComplete = () => {
    setShowBattleSimulationEffects(false);
    setAutoSimulationActive(false);
    setSelectedBattleForces(null);
  };
  
  // Manejadores para planificación de escenarios
  const handleOpenScenarioPlanner = () => {
    setShowScenarioPlanner(true);
  };
  
  const handleCloseScenarioPlanner = () => {
    setShowScenarioPlanner(false);
  };
  
  const handleScenarioSelected = (scenario: any) => {
    setActiveScenario(scenario);
    setShowScenarioPlanner(false);
    
    // Aquí podríamos cargar el escenario en el mapa
    alert(`تم تحميل السيناريو: ${scenario.name}`);
  };
  
  // Handle search result click
  const handleSearchResultClick = (item: Unit | Equipment) => {
    // Check if it's a unit
    if ('commander' in item) {
      setSelectedUnitId(item.id);
      
      // Center map on selected unit
      if (item.location.type === 'polygon') {
        const coords = item.location.coordinates as number[][][];
        if (coords && coords[0] && coords[0][0]) {
          // Calculate center of polygon
          const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
          const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
          
          setMapCenter([centerLat, centerLng]);
          if (mapRef.current) {
            mapRef.current.setView([centerLat, centerLng], 9);
          }
        }
      }
    } else {
      // It's equipment, find its unit and center on that
      const unit = getUnitById(item.unitId || '');
      if (unit) {
        setSelectedUnitId(unit.id);
        
        if (unit.location.type === 'polygon') {
          const coords = unit.location.coordinates as number[][][];
          if (coords && coords[0] && coords[0][0]) {
            // Calculate center of polygon
            const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
            const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
            
            setMapCenter([centerLat, centerLng]);
            if (mapRef.current) {
              mapRef.current.setView([centerLat, centerLng], 9);
            }
          }
        }
      }
    }
  };
  
  // Toggle map layers visibility
  const toggleLayer = (layer: keyof typeof mapLayers) => {
    setMapLayers({
      ...mapLayers,
      [layer]: !mapLayers[layer]
    });
  };
  
  // Handle zoom control
  const handleZoom = (direction: 'in' | 'out') => {
    if (mapRef.current) {
      const currentZoom = mapRef.current.getZoom();
      direction === 'in' 
        ? mapRef.current.setZoom(currentZoom + 1)
        : mapRef.current.setZoom(currentZoom - 1);
    }
  };
  
  // Toggle satellite view
  const toggleSatelliteView = () => {
    setSatelliteView(!satelliteView);
  };
  
  // Select a satellite image
  const selectSatelliteImage = (index: number) => {
    // This would typically update the map tiles, but for this example we'll just toggle the state
    setSatelliteView(true);
    setShowMapGallery(false);
  };
  
  // Helper: حساب المسافة بين نقطتين جغرافيتين (Haversine)
  function getDistanceKm(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // نصف قطر الأرض كم
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  // جلب أنواع الأسلحة المتوفرة لدى القوات الصديقة
  const friendlyWeaponTypes = Array.from(new Set(
    equipmentData.filter(eq => eq.unitId && eq.capabilities.includes('attack') && eq.status === 'operational')
      .map(eq => eq.type)
  ));

  // تصفية القوات الصديقة بناءً على معايير المستخدم
  function getFilteredFriendlyUnitsForTarget(targetUnit: Unit) {
    if (targetUnit.location.type !== 'polygon') return [];
    const targetCoords = targetUnit.location.coordinates as number[][][];
    const targetCenterLat = targetCoords[0].reduce((sum, point) => sum + point[0], 0) / targetCoords[0].length;
    const targetCenterLng = targetCoords[0].reduce((sum, point) => sum + point[1], 0) / targetCoords[0].length;
    
    // Use the current unitsData state which includes moved positions
    return unitsData.filter(unit => {
      if (unit.side !== 'friendly' || unit.location.type !== 'polygon' || unit.readiness < targetingReadiness) return false;
      
      // Get current position of the unit (which may have been moved)
      const coords = unit.location.coordinates as number[][][];
      const unitCenterLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
      const unitCenterLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
      
      const distance = getDistanceKm(unitCenterLat, unitCenterLng, targetCenterLat, targetCenterLng);
      if (distance > targetingDistance) return false;
      
      // Check equipment at current position
      const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational' && (eq.range || 0) >= distance && (targetingWeaponType === 'all' || eq.type === targetingWeaponType));
      return eqs.length > 0;
    }).map(unit => {
      // Get current position of the unit
      const coords = unit.location.coordinates as number[][][];
      const unitCenterLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
      const unitCenterLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
      const distance = getDistanceKm(unitCenterLat, unitCenterLng, targetCenterLat, targetCenterLng);
      
      // Get equipment at current position
      const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational' && (eq.range || 0) >= distance && (targetingWeaponType === 'all' || eq.type === targetingWeaponType));
      const mainWeapon = eqs.sort((a, b) => (b.firepower || 0) - (a.firepower || 0))[0];
      
      return {
        ...unit,
        distance,
        mainWeapon
      };
    });
  }

  // دالة جلب مدى الدوائر للوحدة حسب نوع السلاح
  const getCircleRanges = (unit: Unit) => {
    const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational');
    const mainType = eqs.length > 0 ? eqs[0].type : 'tank';
    const config = circleConfig || {};

    // مدى افتراضي أكبر وأكثر وضوحاً
    const defaultRanges = {
      tank: { blind: 3, kill: 8, nonkill: 15 },
      artillery: { blind: 5, kill: 15, nonkill: 25 },
      missile: { blind: 10, kill: 25, nonkill: 50 },
      anti_air: { blind: 8, kill: 20, nonkill: 35 },
      helicopter: { blind: 15, kill: 30, nonkill: 60 },
      fighter: { blind: 25, kill: 50, nonkill: 100 },
      apc: { blind: 2, kill: 5, nonkill: 10 },
      default: { blind: 3, kill: 8, nonkill: 15 }
    };

    return config[mainType] || defaultRanges[mainType] || defaultRanges.default;
  };

  // Helper: حساب القوات الصديقة التي تقع ضمن دوائر التحليل لقوات العدو
  function getThreatenedFriendlyUnits() {
    const threatenedUnits: any[] = [];

    // جلب جميع وحدات العدو
    const enemyUnits = unitsData.filter(u => u.side === 'enemy' && u.location.type === 'polygon');

    // لكل وحدة صديقة، تحقق من وقوعها ضمن دوائر أي وحدة عدو
    unitsData.filter(u => u.side === 'friendly' && u.location.type === 'polygon').forEach(friendlyUnit => {
      const [friendlyLat, friendlyLng] = getUnitCenter(friendlyUnit);

      let isThreatenedBy: any[] = [];
      let minDist = Infinity;
      let nearestEnemy = null;
      let rangeType = '';

      // تحقق من كل وحدة عدو
      enemyUnits.forEach(enemyUnit => {
        const [enemyLat, enemyLng] = getUnitCenter(enemyUnit);
        const { blind, kill, nonkill } = getCircleRanges(enemyUnit);
        const distance = getDistanceKm(friendlyLat, friendlyLng, enemyLat, enemyLng);

        // تحديد نوع التهديد حسب الدائرة
        let currentRangeType = '';
        if (distance <= blind) {
          currentRangeType = 'عمى - خطر شديد';
          isThreatenedBy.push({ enemy: enemyUnit, distance, rangeType: currentRangeType, severity: 'critical' });
        } else if (distance <= kill) {
          currentRangeType = 'قتل - خطر عالي';
          isThreatenedBy.push({ enemy: enemyUnit, distance, rangeType: currentRangeType, severity: 'high' });
        } else if (distance <= nonkill) {
          currentRangeType = 'عدم قتل - خطر متوسط';
          isThreatenedBy.push({ enemy: enemyUnit, distance, rangeType: currentRangeType, severity: 'medium' });
        }

        // تتبع أقرب عدو
        if (distance < minDist) {
          minDist = distance;
          nearestEnemy = enemyUnit;
          rangeType = currentRangeType;
        }
      });

      // إضافة الوحدة إذا كانت مهددة
      if (isThreatenedBy.length > 0) {
        threatenedUnits.push({
          ...friendlyUnit,
          nearestEnemy,
          minDist,
          rangeType,
          threatenedBy: isThreatenedBy,
          maxSeverity: isThreatenedBy.reduce((max, threat) => {
            const severityOrder = { 'critical': 3, 'high': 2, 'medium': 1 };
            return severityOrder[threat.severity] > severityOrder[max] ? threat.severity : max;
          }, 'medium')
        });
      }
    });

    return threatenedUnits;
  }
  
  // قائمة معرفات الوحدات المهددة في التحليل المكاني
  const threatenedUnitIds = getThreatenedFriendlyUnits().map(u => u.id);

  // دالة للحصول على الأعداء القادرين على استهداف وحدة صديقة معينة
  const getEnemyThreatsToFriendlyUnit = (friendlyUnitId: string) => {
    const friendlyUnit = getUnitById(friendlyUnitId);
    if (!friendlyUnit || friendlyUnit.side !== 'friendly') return [];

    const [friendlyLat, friendlyLng] = getUnitCenter(friendlyUnit);
    const enemyThreats: Array<{
      enemy: Unit;
      circleType: 'blind' | 'kill' | 'noKill';
      severity: 'critical' | 'high' | 'medium';
    }> = [];

    unitsData.filter((u: Unit) => u.side === 'enemy').forEach((enemyUnit: Unit) => {
      const [enemyLat, enemyLng] = getUnitCenter(enemyUnit);
      const distance = getDistanceKm(friendlyLat, friendlyLng, enemyLat, enemyLng);
      const ranges = getCircleRanges(enemyUnit);

      if (distance <= ranges.blind) {
        enemyThreats.push({
          enemy: enemyUnit,
          circleType: 'blind',
          severity: 'critical'
        });
      } else if (distance <= ranges.kill) {
        enemyThreats.push({
          enemy: enemyUnit,
          circleType: 'kill',
          severity: 'high'
        });
      } else if (distance <= ranges.noKill) {
        enemyThreats.push({
          enemy: enemyUnit,
          circleType: 'noKill',
          severity: 'medium'
        });
      }
    });

    return enemyThreats.sort((a, b) => {
      const severityOrder = { critical: 3, high: 2, medium: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  };

  // دالة للحصول على معرفات الأعداء القادرين على استهداف وحدة معينة
  const getEnemyThreatIds = (friendlyUnitId: string): string[] => {
    const threats = getEnemyThreatsToFriendlyUnit(friendlyUnitId);
    return threats.map(threat => threat.enemy.id);
  };

  // دالة لإنشاء أيقونة مخصصة للعتاد
  const createEquipmentIcon = (equipment: Equipment, size: number = 24) => {
    // استخدام رموز إيموجي للعتاد
    const getEquipmentSymbol = (equipment: Equipment) => {
      const type = equipment.type?.toLowerCase() || 'tank';

      let symbol = '';
      switch (type) {
        case 'tank': symbol = '🚗'; break;
        case 'artillery': symbol = '🎯'; break;
        case 'missile': symbol = '🚀'; break;
        case 'anti_air': symbol = '🛡️'; break;
        case 'helicopter': symbol = '🚁'; break;
        case 'fighter': symbol = '✈️'; break;
        case 'drone': symbol = '🛸'; break;
        case 'apc': symbol = '🚐'; break;
        case 'radar': symbol = '📡'; break;
        case 'logistics': symbol = '🚚'; break;
        case 'medical': symbol = '🚑'; break;
        case 'command': symbol = '📻'; break;
        default: symbol = '⚙️'; break;
      }

      return symbol;
    };

    const symbol = getEquipmentSymbol(equipment);
    const bgColor = equipment.status === 'operational' ? '#059669' : '#dc2626';
    const borderColor = equipment.status === 'operational' ? '#047857' : '#991b1b';

    const iconHtml = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        width: ${size}px;
        height: ${size}px;
        background: ${bgColor};
        color: white;
        font-size: ${Math.floor(size * 0.6)}px;
        font-weight: bold;
        border: 2px solid ${borderColor};
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
        z-index: 500;
      ">
        <span style="
          line-height: 1;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        ">${symbol}</span>
      </div>
    `;

    return L.divIcon({
      html: iconHtml,
      className: 'custom-equipment-icon',
      iconSize: [size, size],
      iconAnchor: [size/2, size/2]
    });
  };

  // دالة لإنشاء أيقونة مخصصة للوحدات
  const createCustomIcon = (unit: Unit, size: number = 32) => {
    // استخدام رموز نصية واضحة ومرئية
    const getUnitSymbol = (unit: Unit) => {
      const type = unit.type?.toLowerCase() || 'infantry';

      let symbol = '';
      switch (type) {
        case 'armor': symbol = '🛡️'; break;
        case 'artillery': symbol = '🎯'; break;
        case 'mechanized': symbol = '🚗'; break;
        case 'air_defense': symbol = '🚀'; break;
        case 'logistics': symbol = '📦'; break;
        case 'brigade': symbol = '⚔️'; break;
        case 'battalion': symbol = '⚡'; break;
        case 'company': symbol = '👥'; break;
        default: symbol = '👥'; break;
      }

      return symbol;
    };

    const symbol = getUnitSymbol(unit);
    const bgColor = unit.side === 'friendly' ? '#3b82f6' : '#ef4444';
    const textColor = 'white';
    const borderColor = unit.side === 'friendly' ? '#1e40af' : '#dc2626';

    // تحديد الشكل بناءً على الجانب
    const isEnemy = unit.side === 'enemy';
    const borderRadius = isEnemy ? '0' : '6px';
    const transform = isEnemy ? 'rotate(45deg)' : 'none';
    const symbolTransform = isEnemy ? 'rotate(-45deg)' : 'none';

    const iconHtml = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        width: ${size}px;
        height: ${size}px;
        background: ${bgColor};
        color: ${textColor};
        font-size: ${Math.floor(size * 0.5)}px;
        font-weight: bold;
        border: 3px solid ${borderColor};
        border-radius: ${borderRadius};
        box-shadow: 0 3px 6px rgba(0,0,0,0.4), 0 0 0 1px white;
        transform: ${transform};
        position: relative;
        z-index: 1000;
      ">
        <span style="
          transform: ${symbolTransform};
          line-height: 1;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        ">${symbol}</span>
      </div>
    `;

    return L.divIcon({
      html: iconHtml,
      className: 'custom-military-icon',
      iconSize: [size, size],
      iconAnchor: [size/2, size/2]
    });
  };
  
  // دالة تفعيل تحليل الدوائر للوحدة المختارة
  const handleAnalyzeCirclesSingle = () => {
    setShowCircleAnalysisAll(false);
    setShowCircleAnalysisSingle(true);
    setShowCircleAnalysisFlash(true);
    setSelectedUnitId(null); // إلغاء التحديد السابق
  };
  // دالة تفعيل تحليل الدوائر للجميع
  const handleAnalyzeCirclesAll = () => {
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisAll(true);
    setShowCircleAnalysisFlash(false);
    setSelectedUnitId(null);
  };

  // دالة تفعيل تحليل الدوائر مع التضاريس
  const handleAnalyzeCirclesSingleWithTerrain = () => {
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisAll(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithTerrain(true);
    setShowCircleAnalysisFlash(true);
    setSelectedUnitId(null);
  };

  // دالة تفعيل تحليل الدوائر مع العوائق
  const handleAnalyzeCirclesSingleWithObstacles = () => {
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisAll(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(true);
    setShowCircleAnalysisSingleWithBoth(false);
    setShowCircleAnalysisFlash(true);
    setSelectedUnitId(null);
    setCircleAnalysisMode(null); // إلغاء النمط الجديد
  };

  // دالة تفعيل تحليل الدوائر مع التضاريس والعوائق معاً
  const handleAnalyzeCirclesSingleWithBoth = () => {
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisAll(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(true);
    setShowCircleAnalysisFlash(true);
    setSelectedUnitId(null);
    setCircleAnalysisMode(null); // إلغاء النمط الجديد
  };

  // دوال التحكم الجديدة لأنماط تحليل الدوائر
  const handleCircleAnalysisNormal = () => {
    setCircleAnalysisMode('normal');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleCircleAnalysisWithTerrain = () => {
    setCircleAnalysisMode('terrain');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleCircleAnalysisWithObstacles = () => {
    setCircleAnalysisMode('obstacles');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleCircleAnalysisWithBoth = () => {
    setCircleAnalysisMode('both');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleClearCircleAnalysis = () => {
    setCircleAnalysisMode(null);
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  // دالة جلب مدى السلاح الرئيسي للوحدة
  const getMainWeaponRange = (unit: Unit): number => {
    const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational');
    if (eqs.length === 0) return 5; // افتراضي لدبابة
    return Math.max(...eqs.map(eq => eq.range || 5));
  };
  // رسم دوائر التحليل حول وحدة
  const renderAnalysisCircles = (unit: Unit) => {
    const [centerLat, centerLng] = getUnitCenter(unit);
    const { blind, kill, nonkill } = getCircleRanges(unit);

    // ألوان أكثر وضوحاً للدوائر
    return [
      <Circle
        key={`blind-${unit.id}`}
        center={[centerLat, centerLng]}
        radius={blind * 1000}
        pathOptions={{
          color: '#059669',
          fillColor: '#10b981',
          fillOpacity: 0.2,
          weight: 4,
          opacity: 0.8
        }}
      />,
      <Circle
        key={`kill-${unit.id}`}
        center={[centerLat, centerLng]}
        radius={kill * 1000}
        pathOptions={{
          color: '#dc2626',
          fillColor: '#ef4444',
          fillOpacity: 0.15,
          weight: 4,
          opacity: 0.9
        }}
      />,
      <Circle
        key={`nonkill-${unit.id}`}
        center={[centerLat, centerLng]}
        radius={nonkill * 1000}
        pathOptions={{
          color: '#d97706',
          fillColor: '#f59e0b',
          fillOpacity: 0.1,
          weight: 4,
          dashArray: '10 5',
          opacity: 0.8
        }}
      />
    ];
  };
  
  // بيانات التضاريس المحسنة (جبال، تلال، وديان)
  const terrainFeatures = [
    // جبال القلمون (شمال دمشق)
    {
      type: 'Feature',
      properties: { name: 'جبال القلمون', elevation: 2800, terrainType: 'mountain' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [36.4, 33.8], [36.6, 33.9], [36.7, 33.7], [36.5, 33.6], [36.3, 33.7], [36.4, 33.8]
        ]]
      }
    },
    // جبال الساحل السوري
    {
      type: 'Feature',
      properties: { name: 'جبال الساحل', elevation: 1500, terrainType: 'mountain' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [35.8, 35.5], [36.0, 35.7], [36.1, 35.4], [35.9, 35.2], [35.7, 35.3], [35.8, 35.5]
        ]]
      }
    },
    // جبل الشيخ (جبل حرمون)
    {
      type: 'Feature',
      properties: { name: 'جبل الشيخ', elevation: 2814, terrainType: 'mountain' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [35.7, 33.3], [35.9, 33.4], [36.0, 33.2], [35.8, 33.1], [35.6, 33.2], [35.7, 33.3]
        ]]
      }
    },
    // تلال حوران
    {
      type: 'Feature',
      properties: { name: 'تلال حوران', elevation: 800, terrainType: 'hills' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [36.2, 32.8], [36.5, 33.0], [36.6, 32.7], [36.3, 32.5], [36.1, 32.6], [36.2, 32.8]
        ]]
      }
    },
    // وادي الفرات
    {
      type: 'Feature',
      properties: { name: 'وادي الفرات', elevation: 200, terrainType: 'valley' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [40.0, 35.0], [40.5, 35.2], [40.8, 34.8], [40.3, 34.5], [39.8, 34.7], [40.0, 35.0]
        ]]
      }
    },
    // تلال إدلب
    {
      type: 'Feature',
      properties: { name: 'تلال إدلب', elevation: 600, terrainType: 'hills' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [36.5, 35.8], [36.8, 36.0], [36.9, 35.7], [36.6, 35.5], [36.4, 35.6], [36.5, 35.8]
        ]]
      }
    }
  ];

  // عوائق متنوعة (مدن، مطارات، قواعد عسكرية، أنهار)
  const obstaclePolygons = [
    // مطار دمشق الدولي
    {
      type: 'Feature',
      properties: { name: 'مطار دمشق الدولي', obstacleType: 'airport' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [36.51, 33.41], [36.53, 33.42], [36.52, 33.40], [36.50, 33.39], [36.51, 33.41]
        ]]
      }
    },
    // مطار حلب الدولي
    {
      type: 'Feature',
      properties: { name: 'مطار حلب الدولي', obstacleType: 'airport' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [37.22, 36.18], [37.25, 36.19], [37.24, 36.16], [37.21, 36.15], [37.22, 36.18]
        ]]
      }
    },
    // قاعدة حميميم الجوية
    {
      type: 'Feature',
      properties: { name: 'قاعدة حميميم الجوية', obstacleType: 'military_base' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [35.95, 35.40], [35.98, 35.42], [35.97, 35.38], [35.94, 35.37], [35.95, 35.40]
        ]]
      }
    },
    // مدينة حلب (منطقة حضرية كثيفة)
    {
      type: 'Feature',
      properties: { name: 'مدينة حلب', obstacleType: 'urban_area' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [37.10, 36.15], [37.25, 36.25], [37.30, 36.10], [37.15, 36.05], [37.05, 36.10], [37.10, 36.15]
        ]]
      }
    },
    // مدينة دمشق (منطقة حضرية كثيفة)
    {
      type: 'Feature',
      properties: { name: 'مدينة دمشق', obstacleType: 'urban_area' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [36.25, 33.48], [36.35, 33.55], [36.40, 33.45], [36.30, 33.40], [36.20, 33.45], [36.25, 33.48]
        ]]
      }
    },
    // نهر الفرات (عائق مائي)
    {
      type: 'Feature',
      properties: { name: 'نهر الفرات', obstacleType: 'river' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [40.0, 35.5], [40.8, 35.8], [41.0, 35.6], [40.2, 35.3], [40.0, 35.5]
        ]]
      }
    },
    // نهر العاصي
    {
      type: 'Feature',
      properties: { name: 'نهر العاصي', obstacleType: 'river' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [36.7, 35.1], [36.9, 35.3], [37.0, 35.0], [36.8, 34.8], [36.6, 35.0], [36.7, 35.1]
        ]]
      }
    },
    // منطقة صناعية في حمص
    {
      type: 'Feature',
      properties: { name: 'المنطقة الصناعية - حمص', obstacleType: 'industrial' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [36.70, 34.70], [36.75, 34.72], [36.74, 34.68], [36.69, 34.67], [36.70, 34.70]
        ]]
      }
    },
    // مصفاة بانياس
    {
      type: 'Feature',
      properties: { name: 'مصفاة بانياس', obstacleType: 'refinery' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [35.94, 35.18], [35.97, 35.20], [35.96, 35.16], [35.93, 35.15], [35.94, 35.18]
        ]]
      }
    },
    // سد الفرات
    {
      type: 'Feature',
      properties: { name: 'سد الفرات', obstacleType: 'dam' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [38.05, 35.85], [38.08, 35.87], [38.07, 35.83], [38.04, 35.82], [38.05, 35.85]
        ]]
      }
    },
    // منطقة أثرية - تدمر
    {
      type: 'Feature',
      properties: { name: 'مدينة تدمر الأثرية', obstacleType: 'archaeological' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [38.25, 34.55], [38.30, 34.57], [38.29, 34.53], [38.24, 34.52], [38.25, 34.55]
        ]]
      }
    }
  ];

  // دالة إنشاء مجموعة التضاريس
  const createTerrainCollection = () => {
    return turf.featureCollection(terrainFeatures);
  };

  // دالة إنشاء مجموعة العوائق
  const createObstaclesCollection = () => {
    return turf.featureCollection(obstaclePolygons);
  };

  // دالة محسنة لرسم الدوائر مع قطع التضاريس
  const renderCircleWithTerrain = (unit: Unit) => {
    const [centerLat, centerLng] = getUnitCenter(unit);
    const { blind, kill, nonkill } = getCircleRanges(unit);
    const terrainCollection = createTerrainCollection();

    const renderClippedByTerrain = (radius: number, color: string, fillColor: string, keyPrefix: string) => {
      try {
        // إنشاء دائرة أساسية
        const circle = turf.circle([centerLng, centerLat], radius, { steps: 120, units: 'kilometers' });
        const polygonElements: JSX.Element[] = [];

        // قطع الدائرة بالتضاريس
        let remaining = circle;
        terrainFeatures.forEach((terrain, index) => {
          if (remaining && terrain.geometry.type === 'Polygon') {
            try {
              // تحديد تأثير التضاريس على الرؤية
              const elevationEffect = terrain.properties.elevation > 1000 ? 0.7 : 0.9; // الجبال العالية تحجب أكثر

              const terrainFeature = turf.feature(terrain.geometry);
              const intersection = turf.intersect(remaining, terrainFeature);

              if (intersection) {
                // إضافة منطقة متأثرة بالتضاريس
                const affectedCoords = intersection.geometry.type === 'Polygon'
                  ? [intersection.geometry.coordinates]
                  : intersection.geometry.coordinates;

                affectedCoords.forEach((coords, coordIndex) => {
                  polygonElements.push(
                    <Polygon
                      key={`${keyPrefix}-terrain-affected-${unit.id}-${index}-${coordIndex}`}
                      positions={coords[0].map(coord => [coord[1], coord[0]])}
                      pathOptions={{
                        color: color,
                        fillColor: fillColor,
                        fillOpacity: 0.1 * elevationEffect, // تقليل الشفافية في المناطق المتأثرة
                        weight: 2,
                        dashArray: '5 5',
                        opacity: 0.6
                      }}
                    />
                  );
                });

                // إزالة المنطقة المتأثرة من الدائرة الأساسية
                const difference = turf.difference(remaining, terrainFeature);
                if (difference) remaining = difference;
              }
            } catch (error) {
              console.warn('خطأ في معالجة التضاريس:', error);
            }
          }
        });

        // رسم الجزء المتبقي من الدائرة
        if (remaining && remaining.geometry.type === 'Polygon') {
          const coords = remaining.geometry.coordinates;
          coords.forEach((ring, ringIndex) => {
            polygonElements.push(
              <Polygon
                key={`${keyPrefix}-remaining-${unit.id}-${ringIndex}`}
                positions={ring.map(coord => [coord[1], coord[0]])}
                pathOptions={{
                  color: color,
                  fillColor: fillColor,
                  fillOpacity: 0.2,
                  weight: 3,
                  opacity: 0.8
                }}
              />
            );
          });
        }

        return polygonElements;
      } catch (error) {
        console.warn('خطأ في رسم الدائرة مع التضاريس:', error);
        // العودة إلى دائرة عادية في حالة الخطأ
        return [
          <Circle
            key={`${keyPrefix}-fallback-${unit.id}`}
            center={[centerLat, centerLng]}
            radius={radius * 1000}
            pathOptions={{ color, fillColor, fillOpacity: 0.2, weight: 3 }}
          />
        ];
      }
    };

    return [
      ...renderClippedByTerrain(blind, '#059669', '#10b981', 'blind-terrain'),
      ...renderClippedByTerrain(kill, '#dc2626', '#ef4444', 'kill-terrain'),
      ...renderClippedByTerrain(nonkill, '#d97706', '#f59e0b', 'nonkill-terrain')
    ];
  };
  // دالة محسنة لرسم الدوائر مع قطع العوائق
  const renderCircleWithObstaclesAdvanced = (unit: Unit) => {
    const [centerLat, centerLng] = getUnitCenter(unit);
    const { blind, kill, nonkill } = getCircleRanges(unit);
    const obstaclesCollection = createObstaclesCollection();

    const renderClippedByObstacles = (radius: number, color: string, fillColor: string, keyPrefix: string) => {
      try {
        // إنشاء دائرة أساسية
        const circle = turf.circle([centerLng, centerLat], radius, { steps: 120, units: 'kilometers' });
        const polygonElements: JSX.Element[] = [];

        // قطع الدائرة بالعوائق
        let remaining = circle;
        obstaclePolygons.forEach((obstacle, index) => {
          if (remaining && obstacle.geometry.type === 'Polygon') {
            try {
              // تحديد تأثير العائق على الرؤية
              const obstacleType = obstacle.properties.obstacleType;
              let blockageEffect = 1.0; // تأثير الحجب الكامل افتراضياً

              switch (obstacleType) {
                case 'urban_area': blockageEffect = 0.8; break; // المدن تحجب 80%
                case 'mountain': blockageEffect = 0.9; break; // الجبال تحجب 90%
                case 'forest': blockageEffect = 0.6; break; // الغابات تحجب 60%
                case 'river': blockageEffect = 0.3; break; // الأنهار تحجب 30%
                case 'airport': blockageEffect = 0.7; break; // المطارات تحجب 70%
                case 'military_base': blockageEffect = 0.9; break; // القواعد العسكرية تحجب 90%
                default: blockageEffect = 0.5; break;
              }

              const obstacleFeature = turf.feature(obstacle.geometry);
              const intersection = turf.intersect(remaining, obstacleFeature);

              if (intersection) {
                // إضافة منطقة متأثرة بالعوائق
                const affectedCoords = intersection.geometry.type === 'Polygon'
                  ? [intersection.geometry.coordinates]
                  : intersection.geometry.coordinates;

                affectedCoords.forEach((coords, coordIndex) => {
                  polygonElements.push(
                    <Polygon
                      key={`${keyPrefix}-obstacle-affected-${unit.id}-${index}-${coordIndex}`}
                      positions={coords[0].map(coord => [coord[1], coord[0]])}
                      pathOptions={{
                        color: color,
                        fillColor: '#666666', // لون رمادي للمناطق المحجوبة
                        fillOpacity: 0.1 * blockageEffect,
                        weight: 2,
                        dashArray: '3 3',
                        opacity: 0.5
                      }}
                    />
                  );
                });

                // إزالة المنطقة المحجوبة من الدائرة الأساسية
                const difference = turf.difference(remaining, obstacleFeature);
                if (difference) remaining = difference;
              }
            } catch (error) {
              console.warn('خطأ في معالجة العوائق:', error);
            }
          }
        });

        // رسم الجزء المتبقي من الدائرة (المناطق غير المحجوبة)
        if (remaining && remaining.geometry.type === 'Polygon') {
          const coords = remaining.geometry.coordinates;
          coords.forEach((ring, ringIndex) => {
            polygonElements.push(
              <Polygon
                key={`${keyPrefix}-clear-${unit.id}-${ringIndex}`}
                positions={ring.map(coord => [coord[1], coord[0]])}
                pathOptions={{
                  color: color,
                  fillColor: fillColor,
                  fillOpacity: 0.25,
                  weight: 3,
                  opacity: 0.8
                }}
              />
            );
          });
        }

        return polygonElements;
      } catch (error) {
        console.warn('خطأ في رسم الدائرة مع العوائق:', error);
        // العودة إلى دائرة عادية في حالة الخطأ
        return [
          <Circle
            key={`${keyPrefix}-fallback-${unit.id}`}
            center={[centerLat, centerLng]}
            radius={radius * 1000}
            pathOptions={{ color, fillColor, fillOpacity: 0.2, weight: 3 }}
          />
        ];
      }
    };

    return [
      ...renderClippedByObstacles(blind, '#059669', '#10b981', 'blind-obstacles'),
      ...renderClippedByObstacles(kill, '#dc2626', '#ef4444', 'kill-obstacles'),
      ...renderClippedByObstacles(nonkill, '#d97706', '#f59e0b', 'nonkill-obstacles')
    ];
  };

  // دالة رسم طبقة العوائق على الخريطة
  const renderObstacleLayer = () => {
    if (!mapLayers.obstacles) return null;

    return obstaclePolygons.map((obstacle, index) => {
      if (obstacle.geometry.type === 'Polygon') {
        const coords = obstacle.geometry.coordinates[0];
        const obstacleType = obstacle.properties.obstacleType;

        // تحديد اللون حسب نوع العائق
        let color = '#666666';
        let fillColor = '#999999';

        switch (obstacleType) {
          case 'urban_area': color = '#8b5cf6'; fillColor = '#c4b5fd'; break;
          case 'airport': color = '#06b6d4'; fillColor = '#67e8f9'; break;
          case 'military_base': color = '#dc2626'; fillColor = '#fca5a5'; break;
          case 'river': color = '#0ea5e9'; fillColor = '#7dd3fc'; break;
          case 'industrial': color = '#ea580c'; fillColor = '#fdba74'; break;
          case 'refinery': color = '#7c2d12'; fillColor = '#fed7aa'; break;
          case 'dam': color = '#1e40af'; fillColor = '#93c5fd'; break;
          case 'archaeological': color = '#a3a3a3'; fillColor = '#d4d4d8'; break;
          default: color = '#666666'; fillColor = '#999999'; break;
        }

        return (
          <Polygon
            key={`obstacle-${index}`}
            positions={coords.map(coord => [coord[1], coord[0]])}
            pathOptions={{
              color: color,
              fillColor: fillColor,
              fillOpacity: 0.3,
              weight: 2,
              opacity: 0.7
            }}
          >
            <Popup>
              <div className="p-2">
                <h3 className="font-bold">{obstacle.properties.name}</h3>
                <p>النوع: {obstacleType}</p>
              </div>
            </Popup>
          </Polygon>
        );
      }
      return null;
    });
  };

  // دالة رسم طبقة التضاريس على الخريطة
  const renderTerrainLayer = () => {
    if (!mapLayers.terrain) return null;

    return terrainFeatures.map((terrain, index) => {
      if (terrain.geometry.type === 'Polygon') {
        const coords = terrain.geometry.coordinates[0];
        const terrainType = terrain.properties.terrainType;
        const elevation = terrain.properties.elevation;

        // تحديد اللون حسب نوع التضاريس والارتفاع
        let color = '#10b981';
        let fillColor = '#34d399';

        switch (terrainType) {
          case 'mountain':
            color = elevation > 2000 ? '#7c2d12' : '#a3a3a3';
            fillColor = elevation > 2000 ? '#fed7aa' : '#d4d4d8';
            break;
          case 'hills':
            color = '#65a30d';
            fillColor = '#a3e635';
            break;
          case 'valley':
            color = '#059669';
            fillColor = '#34d399';
            break;
          default:
            color = '#10b981';
            fillColor = '#34d399';
            break;
        }

        return (
          <Polygon
            key={`terrain-${index}`}
            positions={coords.map(coord => [coord[1], coord[0]])}
            pathOptions={{
              color: color,
              fillColor: fillColor,
              fillOpacity: 0.2,
              weight: 2,
              opacity: 0.6
            }}
          >
            <Popup>
              <div className="p-2">
                <h3 className="font-bold">{terrain.properties.name}</h3>
                <p>النوع: {terrainType}</p>
                <p>الارتفاع: {elevation} متر</p>
              </div>
            </Popup>
          </Polygon>
        );
      }
      return null;
    });
  };


  // دوال التحكم الجديدة لأنماط تحليل الدوائر
  const handleCircleAnalysisNormal = () => {
    setCircleAnalysisMode('normal');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleCircleAnalysisWithTerrain = () => {
    setCircleAnalysisMode('terrain');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleCircleAnalysisWithObstacles = () => {
    setCircleAnalysisMode('obstacles');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleCircleAnalysisWithBoth = () => {
    setCircleAnalysisMode('both');
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  const handleClearCircleAnalysis = () => {
    setCircleAnalysisMode(null);
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithBoth(false);
  };

  // دالة محسنة لرسم دوائر التحليل مع 4 أنماط مختلفة
  const renderCircleAnalysisWithMode = (unit: Unit, mode: 'normal' | 'terrain' | 'obstacles' | 'both') => {
    const { blind, kill, nonkill } = getCircleRanges(unit);

    // النمط العادي - دوائر كاملة بدون قطع
    if (mode === 'normal') {
      return renderAnalysisCircles(unit);
    }

    // النمط مع التضاريس فقط - استخدام الدالة المستوردة
    if (mode === 'terrain') {
      try {
        return renderCircleWithTerrain(unit);
      } catch (error) {
        console.warn('خطأ في رسم الدوائر مع التضاريس:', error);
        return renderAnalysisCircles(unit);
      }
    }

    // النمط مع العوائق فقط - استخدام الدالة المستوردة
    if (mode === 'obstacles') {
      try {
        return renderCircleWithObstaclesAdvanced(unit);
      } catch (error) {
        console.warn('خطأ في رسم الدوائر مع العوائق:', error);
        return renderAnalysisCircles(unit);
      }
    }

    // النمط مع التضاريس والعوائق معاً
    if (mode === 'both') {
      try {
        const terrainCircles = renderCircleWithTerrain(unit);
        const obstacleCircles = renderCircleWithObstaclesAdvanced(unit);
        return [...terrainCircles, ...obstacleCircles];
      } catch (error) {
        console.warn('خطأ في رسم الدوائر مع التضاريس والعوائق:', error);
        return renderAnalysisCircles(unit);
      }
    }

    return [];
  };

  return (
    <div className="relative h-screen w-full">
      <div className="absolute inset-0">
        <MapContainer
          center={mapCenter}
          zoom={zoomLevel}
          className="h-full w-full"
          ref={mapRef}
        >
          <TileLayer
            url={satelliteView
              ? "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
              : "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            }
            attribution={satelliteView
              ? '&copy; <a href="https://www.esri.com/">Esri</a>'
              : '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }
          />

          <MapController onMapClick={handleMapClick} />

          {/* رسم الوحدات العسكرية */}
          {mapLayers.friendly && unitsData.filter(unit => unit.side === 'friendly').map(unit => (
            <Polygon
              key={unit.id}
              positions={unit.location.type === 'polygon'
                ? (unit.location.coordinates as number[][][])[0].map(coord => [coord[0], coord[1]])
                : []
              }
              pathOptions={{
                color: selectedUnitId === unit.id ? '#10b981' :
                       selectedFriendlyUnits.includes(unit.id) ? '#f59e0b' : '#3b82f6',
                fillColor: selectedUnitId === unit.id ? '#10b981' :
                          selectedFriendlyUnits.includes(unit.id) ? '#f59e0b' : '#3b82f6',
                fillOpacity: 0.3,
                weight: selectedUnitId === unit.id ? 4 : 2
              }}
              eventHandlers={{
                click: () => handleUnitClick(unit.id)
              }}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-bold">{unit.name}</h3>
                  <p>القائد: {unit.commander}</p>
                  <p>الأفراد: {unit.personnelCount}</p>
                  <p>الجاهزية: {unit.readiness}%</p>
                </div>
              </Popup>
            </Polygon>
          ))}

          {/* رسم قوات العدو */}
          {mapLayers.enemy && unitsData.filter(unit => unit.side === 'enemy').map(unit => (
            <Polygon
              key={unit.id}
              positions={unit.location.type === 'polygon'
                ? (unit.location.coordinates as number[][][])[0].map(coord => [coord[0], coord[1]])
                : []
              }
              pathOptions={{
                color: targetUnitId === unit.id ? '#f59e0b' :
                       selectedEnemyUnits.includes(unit.id) ? '#f59e0b' : '#ef4444',
                fillColor: targetUnitId === unit.id ? '#f59e0b' :
                          selectedEnemyUnits.includes(unit.id) ? '#f59e0b' : '#ef4444',
                fillOpacity: 0.3,
                weight: targetUnitId === unit.id ? 4 : 2
              }}
              eventHandlers={{
                click: () => handleUnitClick(unit.id)
              }}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-bold">{unit.name}</h3>
                  <p>القائد: {unit.commander}</p>
                  <p>الأفراد: {unit.personnelCount}</p>
                  <p>الجاهزية: {unit.readiness}%</p>
                </div>
              </Popup>
            </Polygon>
          ))}

          {/* رسم دوائر التحليل للوحدة المختارة - النمط الجديد */}
          {circleAnalysisMode && selectedUnitId && (
            renderCircleAnalysisWithMode(getUnitById(selectedUnitId)!, circleAnalysisMode)
          )}

          {/* رسم دوائر التحليل للوحدة المختارة - النمط القديم */}
          {showCircleAnalysisSingle && selectedUnitId && !circleAnalysisMode && (
            renderAnalysisCircles(getUnitById(selectedUnitId)!)
          )}

          {/* رسم دوائر التحليل لجميع الوحدات */}
          {showCircleAnalysisAll && unitsData.map(unit => renderAnalysisCircles(unit))}

          {/* رسم دوائر التحليل مع التضاريس للوحدة المختارة */}
          {showCircleAnalysisSingleWithTerrain && selectedUnitId && !circleAnalysisMode && (
            renderCircleWithTerrain(getUnitById(selectedUnitId)!)
          )}

          {/* رسم دوائر التحليل مع العوائق للوحدة المختارة */}
          {showCircleAnalysisSingleWithObstacles && selectedUnitId && !circleAnalysisMode && (
            renderCircleWithObstaclesAdvanced(getUnitById(selectedUnitId)!)
          )}

          {/* رسم دوائر التحليل مع التضاريس والعوائق معاً للوحدة المختارة */}
          {showCircleAnalysisSingleWithBoth && selectedUnitId && !circleAnalysisMode && (
            <>
              {renderCircleWithTerrain(getUnitById(selectedUnitId)!)}
              {renderCircleWithObstaclesAdvanced(getUnitById(selectedUnitId)!)}
            </>
          )}

          {/* رسم طبقة التضاريس */}
          <TerrainLayer showTerrain={mapLayers.terrain} />

          {/* رسم طبقة العوائق */}
          <ObstacleLayer showObstacles={mapLayers.obstacles} />

        </MapContainer>

        {/* Map Controls */}
        <MapControls
          mapLayers={mapLayers}
          onToggleLayer={toggleLayer}
          onZoom={handleZoom}
          onToggleSatelliteView={toggleSatelliteView}
          onToggleMapGallery={() => setShowMapGallery(!showMapGallery)}
          satelliteView={satelliteView}
          targetingMode={targetingMode}
          moveMode={moveMode}
          onToggleTargetingMode={toggleTargetingMode}
          onToggleMoveMode={toggleMoveMode}
          onToggleTerrainAnalysis={() => handleToggleTerrainAnalysis(!showTerrainAnalysis)}
          onToggleRouteAnalysis={() => handleToggleRouteAnalysis(!showRouteAnalysis)}
          showTerrainAnalysis={showTerrainAnalysis}
          showRouteAnalysis={showRouteAnalysis}
          onOpenBattleSimulator={handleOpenBattleSimulator}
          onOpenScenarioPlanner={handleOpenScenarioPlanner}
          onSpatialAnalysisClick={() => setShowSpatialAnalysisUI(true)}
          onAnalyzeCirclesSingle={handleAnalyzeCirclesSingle}
          onAnalyzeCirclesAll={handleAnalyzeCirclesAll}
          onAnalyzeCirclesSingleWithTerrain={handleAnalyzeCirclesSingleWithTerrain}
          onAnalyzeCirclesSingleWithObstacles={handleAnalyzeCirclesSingleWithObstacles}
          onAnalyzeCirclesSingleWithBoth={handleAnalyzeCirclesSingleWithBoth}
          onOpenCircleConfig={() => setShowCircleConfig(true)}
          onEditCirclesSingleWithObstacles={() => setShowEditCirclesSingleWithObstacles(true)}
          onEditCirclesAllWithObstacles={() => setShowEditCirclesAllWithObstacles(true)}
          // أنماط تحليل الدوائر الجديدة
          onCircleAnalysisNormal={handleCircleAnalysisNormal}
          onCircleAnalysisWithTerrain={handleCircleAnalysisWithTerrain}
          onCircleAnalysisWithObstacles={handleCircleAnalysisWithObstacles}
          onCircleAnalysisWithBoth={handleCircleAnalysisWithBoth}
          onClearCircleAnalysis={handleClearCircleAnalysis}
          circleAnalysisMode={circleAnalysisMode}
          battleSelectionMode={battleSelectionMode}
          selectedFriendlyUnits={selectedFriendlyUnits}
          selectedEnemyUnits={selectedEnemyUnits}
          onToggleBattleSelectionMode={toggleBattleSelectionMode}
          onStartAdvancedBattleSimulation={startAdvancedBattleSimulation}
          onClearUnitSelections={clearUnitSelections}
        />

      </div>
    </div>
  );
}
